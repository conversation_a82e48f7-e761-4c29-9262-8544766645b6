import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import { Logger, ILogger } from '../shared/utils/Logger'
import { PocketOption } from './broker/PocketOption'
import { sendToLogViewer } from './utils/LogEmitter'

let logger: ILogger | null = null
let broker: PocketOption | null = null

logger = Logger.getContextLogger('MAIN')

function initBroker(): PocketOption {
  const session = import.meta.env.MAIN_VITE_SESSION_KEY
  const isDemo = import.meta.env.MAIN_VITE_DEMO === 'true'

  const broker = PocketOption.getInstance(session, isDemo)

  return broker
}

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 860,
    height: 790,
    show: false,
    alwaysOnTop: true,
    autoHideMenuBar: true,
    resizable: false,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    },
    title: 'Pocket Option - Partner Trader'
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

function getErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : 'Unknown error'
}

/**
 * Centralized cleanup function to handle all app cleanup tasks
 */
async function performCleanup(): Promise<void> {
  logger!.info('Starting application cleanup...')

  try {
    // Clean up services
    logger!.info('Cleaning up broker connections...')
    await PocketOption.cleanup()

    // Remove IPC listeners
    logger!.info('Removing IPC listeners...')
    ipcMain.removeAllListeners('ping')

    // Close all browser windows
    logger!.info('Closing browser windows...')
    const windows = BrowserWindow.getAllWindows()
    windows.forEach((window) => {
      if (!window.isDestroyed()) {
        window.close()
      }
    })

    // Clean up logger last
    logger!.info('Application cleanup completed successfully')
    await Logger.cleanup()
  } catch (error) {
    logger!.error(`Cleanup error: ${error}`)
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  logger!.info('Application starting...')

  broker = initBroker()

  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  setupBrokerIPCListeners()

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Handle app cleanup before quit
app.on('before-quit', async (event) => {
  logger!.info('App quit requested - performing cleanup...')

  // Prevent immediate quit to allow cleanup
  event.preventDefault()

  try {
    await performCleanup()
  } finally {
    // Force quit after cleanup
    app.exit(0)
  }
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
function setupBrokerIPCListeners(): void {
  // ================ BOT LISTENERS ================
  ipcMain.handle('bot:start', async (): Promise<void> => {
    try {
      if (!broker) {
        throw new Error('Broker not initialized')
      }

      await broker.startBot()
    } catch (error) {
      const message = getErrorMessage(error)
      logger?.error(`Failed to start bot: ${message}`)
      throw error // Re-throw to allow UI to handle the error
    }
  })

  ipcMain.handle('bot:stop', async (): Promise<void> => {
    try {
      if (!broker) {
        throw new Error('Broker not initialized')
      }

      await broker.stopBot()
    } catch (error) {
      const message = getErrorMessage(error)
      logger?.error(`Failed to stop bot: ${message}`)
    }
  })
  // ================ END BOT LISTENERS ================

  // ================ TRADE SETTINGS LISTENERS ================
  ipcMain.on('trade:setSettings', (_, settings: TradeSettings) => {
    if (!broker) {
      throw new Error('Broker not initialized')
    }

    logger?.success('Trade settings updated', JSON.stringify(settings, null, 2))
    broker.setTradeSettings(settings)
  })

  ipcMain.on('trade:newSession', () => {
    if (!broker) {
      throw new Error('Broker not initialized')
    }

    BrowserWindow.getAllWindows().forEach((w) => {
      if (!w.isDestroyed()) {
        w.webContents.send('trade:newSession')
      }
    })
  })
  // ================ END TRADE SETTINGS LISTENERS ================

  ipcMain.handle('broker:getState', () => {
    if (broker) {
      return broker.getState()
    }
    return 'disconnected'
  })

  // ================ BROKER CONNECTION LISTENERS ================
  ipcMain.handle(
    'broker:connect',
    async (_, credentials: { ssid: string; isDemo: number }): Promise<void> => {
      try {
        // Disconnect existing broker if any
        if (broker) {
          await broker.disconnect()
          broker = null
        }

        // Create new broker instance with provided credentials
        // const isDemo = import.meta.env.MAIN_VITE_DEMO === 'true'
        const isDemo = credentials.isDemo === 1
        broker = PocketOption.getInstance(credentials.ssid, isDemo)

        await broker.connect()

        // The connection will be initiated automatically in the constructor
        logger?.success('Broker connection initiated with new credentials')
      } catch (error) {
        const message = getErrorMessage(error)
        logger?.error(`Failed to connect broker: ${message}`)
        throw error
      }
    }
  )

  ipcMain.handle('broker:disconnect', async (): Promise<void> => {
    try {
      if (broker) {
        await broker.disconnect()
        logger?.success('Broker disconnected successfully')
        sendToLogViewer(`❌ Disconnected from PocketOption`, {
          type: 'error',
          time: new Date()
        })
      }
    } catch (error) {
      const message = getErrorMessage(error)
      logger?.error(`Failed to disconnect broker: ${message}`)
      throw error
    }
  })
  // ================ END BROKER CONNECTION LISTENERS ================

  // ================ MONEY MANAGEMENT LISTENERS ================
  ipcMain.handle('money-management:getStatus', () => {
    if (broker) {
      return broker.getMoneyManagementStatus()
    }
    return null
  })

  ipcMain.handle('money-management:getAccountBalance', () => {
    if (broker) {
      return broker.getAccountBalance()
    }
    return 0
  })
  // ================ END MONEY MANAGEMENT LISTENERS ================
}
